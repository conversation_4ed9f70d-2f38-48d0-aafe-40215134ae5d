import React, { useRef, useState, useContext } from "react";
import SideNavLayout from "../layouts/SideNavLayout";
import Button from "../components/Button";
import { useNavigate } from "react-router-dom";
import { addDays, format, max, min, parseISO } from "date-fns";
import { useEffect } from "react";
import { toast } from "react-toastify";
import Table from "../components/Table";
import SearchBar from "../components/SearchBar";
import { useDispatch, useSelector } from "react-redux";
import { fetchGraphData } from "../store/graphDataSlice";
import { dateRangeStartAndEnd, filterByDate, generateName, getPredClass, getRechartsDataForPlot, getTotalOfColumn, transformModelApiObject } from "../helpers";
import client from "../api/client";
import { COLUMN_LABELS, columnNames, columns, mappings } from "../constants";
import MUIDataTable from "../components/MUITable";
import Loader from "../loader/Loader";
import CustomLoader, { ChartLoader } from "../components/CustomLoader";
import DashboardLoader from "../components/DashboardLoader";
import Card from "../components/Card";
import DonutChart from "../components/charts/DonutChart";
import { getDefaultRateData, getKDEData, getNPLDonutData } from "../components/charts/helpers";
import CurrencyLegend from "../components/charts/legends/CurrencyLegend";
import CreditDistributionChart from "../components/charts/CreditDistributionChart";
import { LuUser, LuUserCheck, LuUserCog, LuUserX } from "react-icons/lu";
import numeral from "numeral";
import BarGraph from "../components/charts/BarGraph";
import CreditTrends from "../components/charts/CreditTrends";

import Modal from "../components/modals/Modal";
import { FiX } from 'react-icons/fi'
import * as Yup from 'yup'
import { Formik } from "formik";
import FormInput from "../components/formik/FormInput";
import Submit from "../components/formik/Submit";
import UserContext from "../contexts/UserContext";

const dashStatsSchema = Yup.object().shape({
  name: Yup.string()
    .required()
    .label("Name"),
  start_date: Yup.string().required().label("Start date"),
  end_date: Yup.string().required().label("End date"),
})

export default function Dashboard() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [chartsLoading, setChartsLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [users, setUsers] = useState([]);
  const [loans, setLoans] = useState([]);
  const [gapplicants, setGApplicants] = useState([]);

  const [activeFilter, setActiveFilter] = useState('year')
  const [activeFilterStartDate, setActiveFilterStartDate] = useState('2020-07-01')
  const [activeFilterEndDate, setActiveFilterEndDate] = useState(format(new Date(), "yyyy-MM-dd"))
  const [startDate, setStartDate] = useState('2020-07-01')
  const [endDate, setEndDate] = useState(format(new Date(), "yyyy-MM-dd"))
  const [filter, setFilter] = useState('all')
  const [dashboardData, setDashboardData] = useState({})
  const [customFilters, setCustomFilters] = useState([])
  const { user } = useContext(UserContext)
  const [showCustomFIlterCreationModal, setShowCustomFIlterCreationModal] = useState(false)
  const [taskId, setTaskId] = useState(null);
  const [filteredLoanees, setFilteredLoanees] = useState([])
  const eventSourceRef = useRef(null);

  console.log("Context", user)
  // console.log("Active Filter:", activeFilter);
  // console.log("Active Filter Start Date:", activeFilterStartDate);
  // console.log("Active Filter End Date:", activeFilterEndDate);
  // console.log("Start Date:", startDate);
  // console.log("End Date:", endDate);
  // console.log("Filter:", filter);
  // console.log("Dashboard Data:", dashboardData);


  const getPrediction = async (row) => {
    const body = transformModelApiObject(row);
    setLoading(true);
    try {
      const { data } = await client.post("/predict", body);
      //   toast.success("Sent Successfully", {
      //     position: "top-left",
      //   });
      navigate("/analysis", { state: { formEntry: body, response: data[0] } });
    } catch (error) {
      toast.error("Failed", {
        position: "top-left",
      });
      console.log(error);
    }
    setLoading(false);
  };

  const getPredictionMUI = async (params, event, details) => {
    const body = transformModelApiObject(params.row);
    setLoading(true);
    try {
      const { data } = await client.post("/predict", body);
      //   toast.success("Sent Successfully", {
      //     position: "top-left",
      //   });
      navigate("/analysis", {
        state: { modelBody: body, response: data[0], readableBody: params.row },
      });
      console.log(data);
    } catch (error) {
      toast.error("Failed", {
        position: "top-left",
      });
      console.log(error);
    }
    setLoading(false);
  };

  const getLoanees = async () => {
    setLoading(true);
    try {
      const { data } = await client.get("/loanees");
      // toast.success("Loaded Successfully", {
      //   position: "top-left",
      // });
      setUsers(data);
    } catch (error) {
      toast.error("Failed", {
        position: "top-left",
      });
      console.log(error);
    }
    setLoading(false);
  };

  const getLoans = async () => {
    setLoading(true);
    try {
      const { data } = await client.get("/loans?outcome=completed");
      // toast.success("Loaded Successfully", {
      //   position: "top-left",
      // });
      setLoans(data);
      console.log("Loans: ", data)
    } catch (error) {
      toast.error("Failed", {
        position: "top-left",
      });
      console.log(error);
    }
    setLoading(false);
  };

  const getDashboardData = async () => {
    setLoading(true);
    setChartsLoading(true);
    try {
      const { data } = await client.get(`/fx/dashboard-data?filterType=${filter}${filter == 'date_range' ? `&startDate=${startDate}&endDate=${endDate}` : filter == 'date' ? `&date=${startDate}` : ''}`);
      // toast.success("Loaded Successfully", {
      //   position: "top-left",
      // });
      setDashboardData(data);

      setActiveFilter(filter)
      setActiveFilterStartDate(startDate)
      setActiveFilterEndDate(endDate)
      setFilteredLoanees(filterByDate(loans, "date_updated", { filterType: filter, startDate, endDate, date: startDate }))

    } catch (error) {
      toast.error("Failed to load dashboard data", {
        position: "top-left",
      });
      console.log(error);
    }
    // Add a small delay to make loading states more visible
    setTimeout(() => {
      setLoading(false);
      setChartsLoading(false);
    }, 300);
  };

  const getCustomFilters = async () => {
    setLoading(true);
    try {
      const { data } = await client.get(`/dash-stats`);
      // toast.success("Custom Filters Loaded", {
      //   position: "top-left",
      // });
      setCustomFilters(data);

    } catch (error) {
      toast.error("Failed to load custom filters", {
        position: "top-left",
      });
      console.log(error);
    }
    setLoading(false);
  };

  const createCustomFilter = async (form) => {
    setLoading(true);
    try {
      const { data } = await client.post("/fx/create-dash-stats", form);

      setTaskId(data.task_id);
      toast.info(`Creating custom filter: ${form.name}`, {
        position: "top-left",
      });
      setShowCustomFIlterCreationModal(false)
    } catch (error) {
      toast.error("Failed", {
        position: "top-left",
      });
      console.log(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!taskId) return;

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    // Create a new EventSource to listen for task updates
    const eventSource = new EventSource(`http://localhost:8000/fx/task-status/${taskId}`);
    eventSourceRef.current = eventSource;

    eventSource.onmessage = (event) => {
      const { status } = JSON.parse(event.data);

      console.log(event)

      if (status === 'SUCCESS') {
        // Fetch updated product data when processing is complete
        toast.success("Custom filter ready", {
          position: "top-left",
        });
        getCustomFilters();
        setLoading(false);
        eventSource.close(); // Close the EventSource when processing is complete
        setTaskId(null); // Reset taskId
      } else if (status === 'FAILURE') {
        toast.error("Custom filter creation failed", {
          position: "top-left",
        });
        getCustomFilters();
        setLoading(false);
        eventSource.close(); // Close the EventSource on failure
        setTaskId(null); // Reset taskId
      }
    };

    return () => {
      // Clean up EventSource if the component unmounts or taskId changes
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [taskId]);

  const classes = ["Defaulted", "Repaid"];

  const columns = [
    { field: "id", headerName: "ID", width: 100 },
    {
      field: "full_name",
      headerName: "Full name",
      description: "This column has a value getter and is not sortable.",
      sortable: false,
      width: 260,
    },
    {
      field: "loan_amount",
      headerName: "Loan amount (GHS)",
      width: 230,
      type: "number",
      valueGetter: (value, row) => `${row.loan_amount.toFixed(2)}`,
    },
    {
      field: "duration_in_months",
      headerName: "Loan duration (months)",
      width: 230,
      type: "number",
    },
    {
      field: "purpose",
      headerName: "Purpose",
      width: 190,
      // valueGetter: (value, row) => `${mappings[row.purpose]}`,
    },
    {
      field: "outcome",
      headerName: "Outcome",
      width: 130,
      valueGetter: (value, row) => `${classes[row.outcome]}`,
    },
  ];

  const options = [
    { value: 'all', label: 'All' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'year', label: 'This Year' },
    { value: 'date', label: 'Date' },
    // { value: 'date_range', label: 'Date Range' },
  ];

  // const filteredGApplicants = filterByDate(gapplicants, "date_updated", { filterType: filter, startDate, endDate, date: startDate })

  // const loaneeCreditAmounts = filteredLoanees.map((l) => l.credit_amount);

  useEffect(() => {
    if (["today", "week", "month", "year"].includes(filter)) {
      console.log("Filter: ", filter)
      const { startDate, endDate } = dateRangeStartAndEnd(filter)
      setStartDate(startDate)
      setEndDate(endDate)
    } else if (["date"].includes(filter)) {
      setStartDate('2020-07-01')
      setEndDate(format(new Date(), "yyyy-MM-dd"))
    }
  }, [filter])

  useEffect(() => {
    getLoanees();
    getLoans();
    getDashboardData();
    getCustomFilters();
  }, []);

  useEffect(() => {
    setFilteredLoanees(filterByDate(loans, "date_updated", { filterType: filter, startDate, endDate, date: startDate }))
  }, [loans]);

  // Trigger data fetching when filters change
  useEffect(() => {
    if (filter && startDate && endDate) {
      getDashboardData();
    }
  }, [filter, startDate, endDate]);

  // Show full dashboard loading state when any critical data is loading
  const isFullDashboardLoading = loading || chartsLoading;

  return (
    <SideNavLayout>
      {isFullDashboardLoading ? (
        <CustomLoader />
      ) : (
        <div className="space-y-6">
          {/* Header Section */}
          <div className="space-y-2">
            <p className="text-muted-foreground">
              Monitor loan performance, application metrics, and credit risk analytics
            </p>
          </div>

          {/* Filters Section */}
          <div className="flex gap-4 flex-wrap items-center">
            <div className="flex gap-3 w-full items-center overflow-x-auto pb-2">
              {options.map((option, index) => (
                <button
                  key={index}
                  className={`
                    ${option.value === filter
                      ? 'text-primary-foreground bg-primary border-primary'
                      : 'text-foreground bg-background border-border hover:bg-muted'
                    }
                    cursor-pointer px-4 py-2 rounded-full text-sm font-medium border transition-colors whitespace-nowrap touch-target
                  `}
                  onClick={() => setFilter(option.value)}
                >
                  {option.label}
                </button>
              ))}
            </div>

            {/* Date Range Controls */}
            <div className="flex gap-4 items-end">
              <div className="min-w-[140px]">
                <label className="text-sm font-medium text-foreground mb-2 block">
                  {["date", "today"].includes(filter) ? "Date" : "Start Date"}
                </label>
                <input
                  type="date"
                  disabled={!["date", "date_range"].includes(filter)}
                  className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-xs transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  onChange={(e) => setStartDate(e.target.value)}
                  value={startDate}
                />
              </div>

              {!["date", "today"].includes(filter) && (
                <div className="min-w-[140px]">
                  <label className="text-sm font-medium text-foreground mb-2 block">End Date</label>
                  <input
                    type="date"
                    disabled={!["date", "date_range"].includes(filter)}
                    className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-xs transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                    onChange={(e) => setEndDate(e.target.value)}
                    value={endDate}
                  />
                </div>
              )}

              {(((activeFilterStartDate != startDate) || (activeFilterEndDate != endDate) || (activeFilter != filter)) && options.map((option) => option.value).includes(filter)) && (
                <button
                  onClick={getDashboardData}
                  className="h-9 px-6 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors"
                >
                  Apply Filter
                </button>
              )}
            </div>
          </div>

          {/* Key Metrics Cards */}
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Card>
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="text-sm font-medium text-muted-foreground">Total Applications</div>
                <LuUser className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold text-foreground">
                  {dashboardData?.application_stats?.total?.toLocaleString() || '--'}
                </div>
                <p className="text-xs text-muted-foreground">
                  All loan applications
                </p>
              </div>
            </Card>

            <Card>
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="text-sm font-medium text-muted-foreground">Approved</div>
                <LuUserCheck className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold text-foreground">
                  {dashboardData?.application_stats?.approved?.toLocaleString() || '--'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Successful applications
                </p>
              </div>
            </Card>

            <Card>
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="text-sm font-medium text-muted-foreground">Pending</div>
                <LuUserCog className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold text-foreground">
                  {dashboardData?.application_stats?.pending?.toLocaleString() || '--'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Under review
                </p>
              </div>
            </Card>

            <Card>
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="text-sm font-medium text-muted-foreground">Rejected</div>
                <LuUserX className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold text-foreground">
                  {dashboardData?.application_stats?.rejected?.toLocaleString() || '--'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Declined applications
                </p>
              </div>
            </Card>
          </div>

          {/* Risk Analytics Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-8 gap-4 lg:gap-6">
            <Card title="NPL Ratio" className="lg:col-span-2">
              <DonutChart
                legendComponent={CurrencyLegend}
                showRatio
                ratioIndexToShow={0}
                data={dashboardData?.loan_stats?.npl_donut}
              />
            </Card>

            <Card className="lg:col-span-4">
              <CreditDistributionChart
                data={getKDEData(filteredLoanees, 'loan_amount')}
                height={350}
              />
            </Card>

            <Card title="Default Rate" className="lg:col-span-2">
              <DonutChart
                showRatio
                ratioIndexToShow={0}
                data={dashboardData?.loan_stats?.default_rate}
              />
            </Card>
          </div>

          {/* Credit Trends */}
          <Card className="w-full">
            <CreditTrends
              data={getRechartsDataForPlot(dashboardData?.application_stats?.total, {
                filterType: activeFilter,
                startDate: activeFilterStartDate,
                endDate: activeFilterEndDate,
                date: activeFilterStartDate
              })}
              loanData={filteredLoanees}
            />
          </Card>

          {/* Loan Portfolio Summary */}
          <Card title="Loan Portfolio Summary" className="w-full">
            <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 mt-5">
              <div className="flex-1">
                <div className="text-center lg:text-left mb-6 lg:mb-0">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Total Disbursed</div>
                  <div className="text-3xl lg:text-4xl font-bold text-foreground">
                    GH₵{numeral(dashboardData?.loan_stats?.summary?.total).format("0,0.00")}
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 lg:gap-5 mt-5">
                  <div className="text-center lg:text-left">
                    <div className="text-sm font-medium text-muted-foreground mb-2">Minimum</div>
                    <div className="text-xl lg:text-2xl font-semibold text-foreground">
                      GH₵{numeral(dashboardData?.loan_stats?.summary?.min).format("0,0.00")}
                    </div>
                  </div>
                  <div className="text-center lg:text-left">
                    <div className="text-sm font-medium text-muted-foreground mb-2">Average</div>
                    <div className="text-xl lg:text-2xl font-semibold text-foreground">
                      GH₵{numeral(dashboardData?.loan_stats?.summary?.avg).format("0,0.00")}
                    </div>
                  </div>
                  <div className="text-center lg:text-left">
                    <div className="text-sm font-medium text-muted-foreground mb-2">Maximum</div>
                    <div className="text-xl lg:text-2xl font-semibold text-foreground">
                      GH₵{numeral(dashboardData?.loan_stats?.summary?.max).format("0,0.00")}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Recent Applications Table */}
          <Card title="Recent Applications" className="w-full">
            <div className="mt-4">
              <MUIDataTable
                columns={columns}
                onRowClick={getPredictionMUI}
                rows={filteredLoanees}
                pageSize={8}
              />
            </div>
          </Card>
        </div>
      )}

      <Modal isOpen={showCustomFIlterCreationModal}>
        <div onClick={() => { setShowCustomFIlterCreationModal(false) }} className="w-screen h-screen overflow-y-auto flex flex-col p-10 items-center bg-black/50">
          <div onClick={(e) => { e.stopPropagation() }} className="flex flex-col bg-white px-14 pt-5 pb-10 rounded">
            <div className="flex justify-end"> <FiX className="text-lg cursor-pointer" onClick={() => { setShowCustomFIlterCreationModal(false) }} /></div>
            <div className="text-xl font-semibold">Create Custom Filter</div>
            <div className="flex-1 w-full">
              <Formik
                initialValues={{ name: '', start_date: '', end_date: '' }}
                validationSchema={dashStatsSchema}
                onSubmit={createCustomFilter}
              >
                <div>
                  <div className="flex flex-col mt-10">
                    <div className="grid grid-cols-3 gap-x-5 gap-y-8">
                      <FormInput name={'name'} type={'text'} label={'Product name'} placeholder="Custom filter name" />
                      <FormInput name={'start_date'} type={'date'} label={'Start date'} />
                      <FormInput name={'end_date'} type={'date'} label={'End date'} />
                    </div>
                  </div>

                  <div className="flex w-full justify-between mt-8">
                    {
                      ((loading) ?
                        <div className={'bg-surface-light/70 text-white px-4 py-3 text-center text-sm rounded'}>Loading...</div>
                        :
                        <Submit className={'bg-surface-light text-white px-4 py-3 text-sm rounded'} text={'Submit'} />)
                    }
                  </div>
                </div>
              </Formik>

            </div>
          </div>
        </div>
      </Modal>
    </SideNavLayout>
  );
}
