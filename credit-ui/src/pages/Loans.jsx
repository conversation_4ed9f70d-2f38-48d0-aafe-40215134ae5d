import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import Table from "../components/Table";
import SideNavLayout from "../layouts/SideNavLayout";
import { applicationTypes, columnNames } from "../constants";
import client from "../api/client";
import { getApplicantInfoField, getPredictionMUI, transformApplicationToModelApiObject, transformModelApiObject } from "../helpers";
import { useNavigate } from "react-router-dom";
import MUIDataTable from "../components/MUITable";
import Loader from "../loader/Loader";
import SearchBar from "../components/SearchBar";
import ActionButton from "../components/ActionButton";
import Modal from "../components/modals/Modal";
import { capitalize } from "@mui/material";

const classes = ["Defaulted", "Repaid"];

const columns = [
    { field: "id", headerName: "ID", width: 100 },
    {
        field: "full_name",
        headerName: "Full name",
        description: "This column has a value getter and is not sortable.",
        sortable: false,
        width: 260,
    },
    {
        field: "loan_amount",
        headerName: "Loan amount (GHS)",
        width: 230,
        type: "number",
        valueGetter: (value, row) => `${row.loan_amount.toFixed(2)}`,
    },
    {
        field: "duration_in_months",
        headerName: "Loan duration (months)",
        width: 230,
        type: "number",
    },
    {
        field: "purpose",
        headerName: "Purpose",
        width: 190,
        // valueGetter: (value, row) => `${mappings[row.purpose]}`,
    },
    {
        field: "outcome",
        headerName: "Outcome",
        width: 130,
        valueGetter: (value, row) => `${classes[row.outcome] || 'Pending'}`,
    },
];

export default function Loans() {
    const [loans, setLoans] = useState([]);
    const [loansType, setLoansType] = useState('pending');
    const [loading, setLoading] = useState(false);
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [searchText, setSearchText] = useState('');

    const navigate = useNavigate();

    const getLoans = async () => {
        setLoading(true);
        try {
            const { data } = await client.get(`/loans?outcome=${loansType}`);
            // toast.success("Loaded Successfully", {
            //   position: "top-left",
            // });
            setLoans(data);
        } catch (error) {
            toast.error("Failed", {
                position: "top-left",
            });
            console.log(error);
        }
        setLoading(false);
    };

    const getPredictionMUI = async (params, event, details) => {
        const body = transformModelApiObject(params.row);
        setLoading(true);
        try {
            const { data } = await client.post("/predict", body);
            //   toast.success("Sent Successfully", {
            //     position: "top-left",
            //   });
            navigate("/analysis", {
                state: { modelBody: body, response: data[0], readableBody: params.row },
            });
            console.log(data);
        } catch (error) {
            toast.error("Failed", {
                position: "top-left",
            });
            console.log(error);
        }
        setLoading(false);
    };


    useEffect(() => {
        getLoans();
    }, [loansType]);

    const LOAN_TYPES = ["pending", "completed", "defaulted", "repaid"]

    return (
        <SideNavLayout>
            <Modal isOpen={isCreateModalOpen}>
                <div onClick={() => { setIsCreateModalOpen(false) }} className="w-screen h-screen flex flex-col p-10 items-center bg-black/50">
                    <div onClick={(e) => { e.stopPropagation() }} className="w-[50%] h-full bg-white">

                    </div>
                </div>
            </Modal>
            <Modal isOpen={isDetailModalOpen}>
                <div onClick={() => { setIsDetailModalOpen(false) }} className="w-screen h-screen flex flex-col p-10 items-center bg-black/50">
                    <div onClick={(e) => { e.stopPropagation() }} className="w-full h-full bg-white">

                    </div>
                </div>
            </Modal>
            <div className="flex flex-col h-full">
                {loading ? (
                    <div className="flex-1 flex flex-col items-center justify-center min-h-[400px]">
                        <Loader height={200} width={200} />
                        <div className="font-semibold text-foreground mt-4">Loading loans...</div>
                    </div>
                ) : (
                    <>
                        {/* Header Controls - Responsive */}
                        <div className="flex flex-col lg:flex-row gap-4 px-4 py-2">
                            {/* Search Bar */}
                            <div className="flex-1 lg:max-w-md">
                                <SearchBar placeholder={'Applicant name'} />
                            </div>

                            {/* Loan Type Filters - Hidden on mobile, shown on tablet+ */}
                            <div className="hidden md:flex items-center gap-4 lg:gap-6">
                                {LOAN_TYPES.map((loan_type, index) => (
                                    <div key={index} onClick={() => { setLoansType(loan_type) }} className={`flex items-center gap-2 text-foreground cursor-pointer hover:text-primary text-sm justify-between touch-target`}>
                                        <div>{capitalize(loan_type)}</div>
                                        <input
                                            type="radio"
                                            value={loan_type}
                                            checked={loansType === loan_type}
                                            onChange={() => { setLoansType(loan_type) }}
                                            style={{ accentColor: "hsl(var(--primary))" }}
                                            className="w-4 h-4 border-[1px]"
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Mobile Loan Type Filters */}
                        <div className="md:hidden px-4 pb-2">
                            <div className="flex flex-wrap gap-2">
                                {LOAN_TYPES.map((loan_type, index) => (
                                    <button
                                        key={index}
                                        onClick={() => { setLoansType(loan_type) }}
                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors touch-target ${loansType === loan_type
                                                ? 'bg-primary text-primary-foreground'
                                                : 'bg-muted text-muted-foreground hover:bg-muted/80'
                                            }`}
                                    >
                                        {capitalize(loan_type)}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Table Section */}
                        <div className="flex-1 min-h-0">
                            <MUIDataTable
                                columns={columns}
                                pageSize={10}
                                onRowClick={getPredictionMUI}
                                rows={loans.filter((loan) =>
                                    loan.full_name.toLowerCase().search(searchText.toLowerCase()) !== -1
                                )}
                                loading={loading}
                            />
                        </div>
                    </>
                )}
            </div>
        </SideNavLayout>
    );
}
